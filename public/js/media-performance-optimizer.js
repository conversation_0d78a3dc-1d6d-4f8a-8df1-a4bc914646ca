/**
 * Media Performance Optimizer
 * Handles camera permissions, canvas optimization, and Razorpay integration issues
 */

class MediaPerformanceOptimizer {
    constructor() {
        this.canvasContexts = new Map();
        this.mediaStreams = new Map();
        this.razorpayInstances = new Map();
        this.init();
    }

    init() {
        this.suppressDeprecatedWarnings();
        this.optimizeCanvasPerformance();
        this.setupCameraPermissions();
        this.setupRazorpayErrorHandling();
        this.setupPerformanceMonitoring();
    }

    /**
     * Suppress deprecated Feature Policy warnings and other noise
     */
    suppressDeprecatedWarnings() {
        // Production environment - disable all console output
        if (typeof window !== 'undefined' && window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
            console.log = function() {};
            console.warn = function() {};
            console.info = function() {};
            console.debug = function() {};
            // Keep console.error for critical issues
        }
    }

    /**
     * Optimize canvas performance to prevent readback warnings
     */
    optimizeCanvasPerformance() {
        // Override canvas getContext to add willReadFrequently attribute
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        
        HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes = {}) {
            if (contextType === '2d') {
                // Add willReadFrequently for better performance
                contextAttributes.willReadFrequently = true;
                contextAttributes.alpha = contextAttributes.alpha !== false;
            }

            const context = originalGetContext.call(this, contextType, contextAttributes);

            if (context && contextType === '2d') {
                // Store context reference for cleanup
                window.mediaOptimizer?.canvasContexts.set(this, context);

                // Optimize image data operations - call on the correct instance
                if (window.mediaOptimizer && typeof window.mediaOptimizer.optimizeImageDataOperations === 'function') {
                    window.mediaOptimizer.optimizeImageDataOperations(context);
                }
            }

            return context;
        };
    }

    optimizeImageDataOperations(context) {
        const originalGetImageData = context.getImageData;
        let lastImageDataTime = 0;
        const throttleDelay = 16; // ~60fps

        context.getImageData = function(...args) {
            const now = performance.now();
            if (now - lastImageDataTime < throttleDelay) {
                // Return cached data if called too frequently
                return this._cachedImageData || originalGetImageData.apply(this, args);
            }
            
            lastImageDataTime = now;
            const imageData = originalGetImageData.apply(this, args);
            this._cachedImageData = imageData;
            return imageData;
        };
    }

    /**
     * Setup enhanced camera permissions handling
     */
    async setupCameraPermissions() {
        try {
            // Check if permissions API is available
            if ('permissions' in navigator) {
                const cameraPermission = await navigator.permissions.query({ name: 'camera' });
                const microphonePermission = await navigator.permissions.query({ name: 'microphone' });

                // Listen for permission changes
                cameraPermission.addEventListener('change', () => {
                    this.handlePermissionChange('camera', cameraPermission.state);
                });

                microphonePermission.addEventListener('change', () => {
                    this.handlePermissionChange('microphone', microphonePermission.state);
                });
            }
        } catch (error) {
            // Silently handle permissions API errors to avoid console spam
        }
    }

    handlePermissionChange(type, state) {
        // Emit custom events for permission changes
        const event = new CustomEvent('permissionChange', {
            detail: { type, state }
        });
        document.dispatchEvent(event);
    }

    /**
     * Enhanced media stream handling
     */
    async requestMediaStream(constraints = { video: true, audio: false }) {
        try {
            // Add willReadFrequently hint for video processing
            if (constraints.video && typeof constraints.video === 'object') {
                constraints.video.willReadFrequently = true;
            }

            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // Store stream reference for cleanup
            const streamId = this.generateStreamId();
            this.mediaStreams.set(streamId, stream);
            
            // Add stream cleanup on page unload
            window.addEventListener('beforeunload', () => {
                this.cleanupMediaStream(streamId);
            });
            
            return { stream, streamId };
        } catch (error) {
            console.error('Media stream request failed:', error);
            this.handleMediaError(error);
            throw error;
        }
    }

    cleanupMediaStream(streamId) {
        const stream = this.mediaStreams.get(streamId);
        if (stream) {
            stream.getTracks().forEach(track => {
                track.stop();
            });
            this.mediaStreams.delete(streamId);
        }
    }

    handleMediaError(error) {
        let userMessage = 'Media access failed';
        
        switch (error.name) {
            case 'NotAllowedError':
                userMessage = 'Camera/microphone access denied. Please allow permissions and refresh.';
                break;
            case 'NotFoundError':
                userMessage = 'No camera/microphone found on this device.';
                break;
            case 'NotReadableError':
                userMessage = 'Camera/microphone is already in use by another application.';
                break;
            case 'OverconstrainedError':
                userMessage = 'Camera/microphone constraints cannot be satisfied.';
                break;
        }
        
        // Emit error event
        const event = new CustomEvent('mediaError', {
            detail: { error, userMessage }
        });
        document.dispatchEvent(event);
    }

    /**
     * Setup Razorpay error handling and debugging
     */
    setupRazorpayErrorHandling() {
        // Check if Razorpay is available, if not, set up a listener for when it loads
        if (typeof Razorpay !== 'undefined') {
            this.enhanceRazorpay();
        } else {
            // Set up a listener for when Razorpay script loads
            const checkRazorpay = () => {
                if (typeof Razorpay !== 'undefined') {
                    this.enhanceRazorpay();
                    console.info('Razorpay detected and enhanced');
                } else {
                    // Check again in 1 second
                    setTimeout(checkRazorpay, 1000);
                }
            };
            setTimeout(checkRazorpay, 100);
        }
    }

    enhanceRazorpay() {
        const originalRazorpay = window.Razorpay;

        window.Razorpay = function(options) {
            // Validate required options
            if (!options.key) {
                console.error('Razorpay: Missing required key parameter');
                throw new Error('Razorpay key is required');
            }

            if (!options.amount || options.amount <= 0) {
                console.error('Razorpay: Invalid amount parameter');
                throw new Error('Razorpay amount must be greater than 0');
            }

            // Add error handling and debugging
            const originalHandler = options.handler;
            const originalModal = options.modal || {};

            options.handler = function(response) {
                console.info('Razorpay payment successful:', response);
                if (originalHandler) {
                    originalHandler(response);
                }
            };

            options.modal = {
                ...originalModal,
                ondismiss: function() {
                    console.info('Razorpay modal dismissed');
                    if (originalModal.ondismiss) {
                        originalModal.ondismiss();
                    }
                },
                escape: originalModal.escape !== false,
                backdropclose: originalModal.backdropclose !== false
            };

            // Add network error handling
            try {
                console.info('Creating Razorpay instance with options:', {
                    key: options.key,
                    amount: options.amount,
                    currency: options.currency,
                    order_id: options.order_id
                });

                const instance = new originalRazorpay(options);

                // Store instance for debugging
                const instanceId = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                window.mediaOptimizer?.razorpayInstances.set(instanceId, instance);

                return instance;
            } catch (error) {
                console.error('Razorpay initialization error:', error);
                throw error;
            }
        };

        // Intercept fetch requests to Razorpay API to handle errors gracefully
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];

            if (typeof url === 'string' && url.includes('api.razorpay.com')) {
                return originalFetch.apply(this, args)
                    .then(response => {
                        // Handle response silently
                        return response;
                    })
                    .catch(error => {
                        // Handle errors silently
                        throw error;
                    });
            }

            return originalFetch.apply(this, args);
        };

        // Production environment - minimal error handling
        // Errors are handled silently to maintain clean console
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        // Monitor canvas operations
        let canvasOperationCount = 0;
        const startTime = performance.now();
        
        setInterval(() => {
            canvasOperationCount = 0;
        }, 1000);

        // Monitor memory usage if available
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    this.performCleanup();
                }
            }, 30000); // Check every 30 seconds
        }
    }

    /**
     * Perform cleanup operations
     */
    performCleanup() {
        // Cleanup unused canvas contexts
        this.canvasContexts.forEach((context, canvas) => {
            if (!document.contains(canvas)) {
                this.canvasContexts.delete(canvas);
            }
        });
        
        // Cleanup unused media streams
        this.mediaStreams.forEach((stream, id) => {
            if (!stream.active) {
                this.cleanupMediaStream(id);
            }
        });
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }

    generateStreamId() {
        return 'stream_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateInstanceId() {
        return 'razorpay_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Debug Razorpay configuration
     */
    async debugRazorpay() {
        try {
            const response = await fetch('/admin/razorpay-debug/test', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();
            return result;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

// Initialize the optimizer when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.mediaOptimizer = new MediaPerformanceOptimizer();
    });
} else {
    window.mediaOptimizer = new MediaPerformanceOptimizer();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MediaPerformanceOptimizer;
}
