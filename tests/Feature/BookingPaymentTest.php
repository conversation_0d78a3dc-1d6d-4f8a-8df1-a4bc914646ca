<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\TimeSpendingBooking;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BookingPaymentTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up default settings
        Setting::set('platform_fee', 50);
        Setting::set('commission_percentage', 10);
    }

    /** @test */
    public function it_enforces_minimum_30_minute_billing_for_new_bookings()
    {
        // Create users
        $client = User::factory()->create(['role' => 'user']);
        $provider = User::factory()->create(['role' => 'user', 'hourly_rate' => 1000]);

        // Test booking for 15 minutes (should be billed as 30 minutes)
        $response = $this->actingAs($client)->postJson('/api/bookings', [
            'provider_id' => $provider->id,
            'booking_date' => now()->addDay()->format('Y-m-d H:i:s'),
            'duration_hours' => 0.5, // 30 minutes billing
            'actual_duration_hours' => 0.25, // 15 minutes actual
            'location' => 'Test Location',
            'notes' => 'Test booking'
        ]);

        $response->assertStatus(200);
        $booking = TimeSpendingBooking::first();
        
        // Should be billed for 30 minutes minimum
        $this->assertEquals(0.5, $booking->duration_hours); // Billing duration
        $this->assertEquals(0.25, $booking->actual_duration_hours); // Actual duration
        $this->assertEquals(500, $booking->base_amount); // 0.5 * 1000
        $this->assertEquals(550, $booking->total_amount); // 500 + 50 platform fee
    }

    /** @test */
    public function it_calculates_difference_payment_for_booking_updates()
    {
        // Create users with wallet balance
        $client = User::factory()->create(['role' => 'user']);
        $client->getWallet()->update(['balance' => 1000]);

        $provider = User::factory()->create(['role' => 'user', 'hourly_rate' => 1000]);

        // Create initial booking (30 minutes)
        $booking = TimeSpendingBooking::create([
            'client_id' => $client->id,
            'provider_id' => $provider->id,
            'booking_date' => now()->addDay(),
            'duration_hours' => 0.5, // 30 minutes
            'actual_duration_hours' => 0.5,
            'hourly_rate' => 1000,
            'platform_fee' => 50,
            'base_amount' => 500,
            'total_amount' => 550,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'meeting_location' => 'Test Location'
        ]);

        // Update booking to 1 hour
        $response = $this->actingAs($client)->putJson("/api/bookings/{$booking->id}", [
            'booking_date' => $booking->booking_date->format('Y-m-d H:i:s'),
            'duration_hours' => 1.0, // 1 hour billing
            'actual_duration_hours' => 1.0, // 1 hour actual
            'location' => 'Test Location',
            'notes' => 'Updated booking'
        ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertTrue($data['success']);
        
        // Check payment breakdown
        $breakdown = $data['payment_breakdown'];
        $this->assertEquals(550, $breakdown['original_amount']); // Original: 0.5 * 1000 + 50
        $this->assertEquals(1050, $breakdown['new_amount']); // New: 1.0 * 1000 + 50
        $this->assertEquals(500, $breakdown['difference_amount']); // Difference: 500
        $this->assertEquals(500, $breakdown['wallet_usage']); // Wallet covers difference
        $this->assertEquals(0, $breakdown['online_payment_required']); // No online payment needed
        
        // Verify wallet balance was deducted
        $this->assertEquals(500, $client->getWallet()->fresh()->balance);
    }

    /** @test */
    public function it_handles_booking_reduction_with_refund()
    {
        // Create users
        $client = User::factory()->create(['role' => 'user']);
        $provider = User::factory()->create(['role' => 'user', 'hourly_rate' => 1000]);

        // Create initial booking (1 hour)
        $booking = TimeSpendingBooking::create([
            'client_id' => $client->id,
            'provider_id' => $provider->id,
            'booking_date' => now()->addDay(),
            'duration_hours' => 1.0, // 1 hour
            'actual_duration_hours' => 1.0,
            'hourly_rate' => 1000,
            'platform_fee' => 50,
            'base_amount' => 1000,
            'total_amount' => 1050,
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'meeting_location' => 'Test Location'
        ]);

        $originalWalletBalance = $client->getWallet()->balance;

        // Update booking to 30 minutes
        $response = $this->actingAs($client)->putJson("/api/bookings/{$booking->id}", [
            'booking_date' => $booking->booking_date->format('Y-m-d H:i:s'),
            'duration_hours' => 0.5, // 30 minutes billing
            'actual_duration_hours' => 0.5, // 30 minutes actual
            'location' => 'Test Location',
            'notes' => 'Reduced booking'
        ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertTrue($data['success']);
        
        // Check payment breakdown
        $breakdown = $data['payment_breakdown'];
        $this->assertEquals(1050, $breakdown['original_amount']); // Original: 1.0 * 1000 + 50
        $this->assertEquals(550, $breakdown['new_amount']); // New: 0.5 * 1000 + 50
        $this->assertEquals(-500, $breakdown['difference_amount']); // Difference: -500 (refund)
        $this->assertEquals(500, $breakdown['refund_amount']); // Refund amount
        
        // Verify wallet balance was credited
        $this->assertEquals($originalWalletBalance + 500, $client->getWallet()->fresh()->balance);
    }

    /** @test */
    public function it_demonstrates_30_minute_minimum_billing_system()
    {
        echo "\n=== 30-Minute Minimum Billing System Demo ===\n";

        // Create users
        $client = User::factory()->create(['role' => 'user']);
        $client->getWallet()->update(['balance' => 2000]);

        $provider = User::factory()->create(['role' => 'user', 'hourly_rate' => 1000]);

        echo "Provider hourly rate: ₹1000\n";
        echo "Platform fee: ₹50\n";
        echo "Client wallet balance: ₹2000\n\n";

        // Test 1: Book 15 minutes (billed as 30 minutes)
        echo "Test 1: Book 15 minutes (actual) -> Billed as 30 minutes\n";
        $booking = TimeSpendingBooking::create([
            'client_id' => $client->id,
            'provider_id' => $provider->id,
            'booking_date' => now()->addDay(),
            'duration_hours' => 0.5, // 30 minutes billing
            'actual_duration_hours' => 0.25, // 15 minutes actual
            'hourly_rate' => 1000,
            'platform_fee' => 50,
            'base_amount' => 500, // 0.5 * 1000
            'total_amount' => 550, // 500 + 50
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'meeting_location' => 'Test Location'
        ]);

        echo "- Actual duration: 15 minutes\n";
        echo "- Billing duration: 30 minutes\n";
        echo "- Amount charged: ₹550 (₹500 base + ₹50 platform fee)\n\n";

        // Test 2: Update to 45 minutes (pay difference only)
        echo "Test 2: Update to 45 minutes -> Pay difference only\n";
        $originalAmount = $booking->total_amount;
        $newBaseAmount = 1000 * 0.75; // 45 minutes = 0.75 hours
        $newTotalAmount = $newBaseAmount + 50;
        $difference = $newTotalAmount - $originalAmount;

        echo "- Original amount: ₹{$originalAmount}\n";
        echo "- New amount: ₹{$newTotalAmount}\n";
        echo "- Difference to pay: ₹{$difference}\n";
        echo "- Payment method: Wallet (sufficient balance)\n\n";

        // Test 3: Update to 2 hours (pay difference only)
        echo "Test 3: Update to 2 hours -> Pay larger difference\n";
        $newBaseAmount2 = 1000 * 2; // 2 hours
        $newTotalAmount2 = $newBaseAmount2 + 50;
        $difference2 = $newTotalAmount2 - $originalAmount;

        echo "- Original amount: ₹{$originalAmount}\n";
        echo "- New amount: ₹{$newTotalAmount2}\n";
        echo "- Difference to pay: ₹{$difference2}\n";
        echo "- Payment method: Wallet (₹{$difference2}) + Online (₹0)\n\n";

        echo "=== Key Benefits ===\n";
        echo "✅ Minimum 30-minute billing ensures fair provider compensation\n";
        echo "✅ Difference-only payments for updates save money for clients\n";
        echo "✅ Automatic wallet usage minimizes online payment friction\n";
        echo "✅ Transparent pricing shows exactly what clients pay\n";
        echo "==========================================\n";
    }
}
