<?php

namespace Tests\Feature;

use App\Http\Controllers\Auth\GoogleLoginController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class GoogleProfilePictureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Use fake storage for testing
        Storage::fake('public');
    }

    /** @test */
    public function it_can_download_and_store_google_profile_picture()
    {
        // Mock HTTP response with fake image data (needs to be larger than 100 bytes)
        $fakeImageContent = str_repeat('fake-image-content-for-testing-', 10); // Make it larger
        Http::fake([
            '*' => Http::response($fakeImageContent, 200, [
                'Content-Type' => 'image/jpeg'
            ])
        ]);

        $controller = new GoogleLoginController();
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('downloadAndStoreProfilePicture');
        $method->setAccessible(true);

        // Test the method
        $result = $method->invoke($controller, 'https://example.com/avatar.jpg', 'test-google-id');

        // Assert that a path was returned
        $this->assertNotNull($result);
        $this->assertStringContainsString('profile-pictures/', $result);
        $this->assertStringContainsString('google_test-google-id', $result);

        // Assert that the file was stored
        Storage::disk('public')->assertExists($result);
    }

    /** @test */
    public function it_handles_invalid_avatar_url_gracefully()
    {
        $controller = new GoogleLoginController();
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('downloadAndStoreProfilePicture');
        $method->setAccessible(true);

        // Test with null URL
        $result = $method->invoke($controller, null, 'test-google-id');
        $this->assertNull($result);

        // Test with empty URL
        $result = $method->invoke($controller, '', 'test-google-id');
        $this->assertNull($result);
    }

    /** @test */
    public function it_handles_http_errors_gracefully()
    {
        // Mock HTTP response with error
        Http::fake([
            '*' => Http::response('Not Found', 404)
        ]);

        $controller = new GoogleLoginController();
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('downloadAndStoreProfilePicture');
        $method->setAccessible(true);

        // Test the method
        $result = $method->invoke($controller, 'https://example.com/nonexistent.jpg', 'test-google-id');

        // Assert that null was returned
        $this->assertNull($result);
    }
}
