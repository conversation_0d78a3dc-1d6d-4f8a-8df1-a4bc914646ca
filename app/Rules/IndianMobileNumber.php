<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class IndianMobileNumber implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Remove all non-digit characters
        $cleanNumber = preg_replace('/\D/', '', $value);
        
        // Check if it's exactly 10 digits and starts with 6, 7, 8, or 9
        if (!preg_match('/^[6-9]\d{9}$/', $cleanNumber)) {
            $fail('The :attribute must be a valid 10-digit Indian mobile number starting with 6, 7, 8, or 9.');
        }
    }

    /**
     * Format phone number to 10 digits
     */
    public static function formatPhoneNumber($phoneNumber)
    {
        // Remove all non-digit characters
        $cleanNumber = preg_replace('/\D/', '', $phoneNumber);
        
        // Handle different phone number formats
        if (strlen($cleanNumber) === 10) {
            // Already 10 digits (Indian mobile format)
            return $cleanNumber;
        } elseif (strlen($cleanNumber) === 11 && substr($cleanNumber, 0, 1) === '0') {
            // 11 digits starting with 0 (remove leading 0)
            return substr($cleanNumber, 1);
        } elseif (strlen($cleanNumber) === 12 && substr($cleanNumber, 0, 2) === '91') {
            // 12 digits starting with 91 (remove country code)
            return substr($cleanNumber, 2);
        } elseif (strlen($cleanNumber) === 13 && substr($cleanNumber, 0, 3) === '+91') {
            // 13 digits starting with +91 (remove country code)
            return substr($cleanNumber, 3);
        }
        
        // If none of the above formats match, check if it's a valid 10-digit number
        if (strlen($cleanNumber) === 10 && preg_match('/^[6-9]\d{9}$/', $cleanNumber)) {
            return $cleanNumber;
        }
        
        // Invalid phone number format
        return null;
    }
}
