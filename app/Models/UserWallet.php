<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserWallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'balance',
        'total_earned',
        'total_withdrawn',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'total_withdrawn' => 'decimal:2',
    ];

    /**
     * Get the user that owns the wallet.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the wallet transactions.
     */
    public function transactions(): Has<PERSON>any
    {
        return $this->hasMany(WalletTransaction::class, 'user_id', 'user_id');
    }

    /**
     * Add money to wallet.
     */
    public function addMoney($amount, $description, $bookingId = null, $metadata = [], $paymentMethod = 'wallet'): WalletTransaction
    {
        $this->increment('balance', $amount);

        // Only increment total_earned if this is not a refund
        $isRefund = isset($metadata['refund_type']) ||
                   strpos(strtolower($description), 'refund') !== false ||
                   strpos(strtolower($description), 'auto-refund') !== false;

        if (!$isRefund) {
            $this->increment('total_earned', $amount);
        }

        return WalletTransaction::create([
            'user_id' => $this->user_id,
            'booking_id' => $bookingId,
            'type' => $isRefund ? 'refund' : 'credit',
            'amount' => $amount,
            'final_amount' => $amount,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'metadata' => $metadata,
            'payment_method' => $paymentMethod,
        ]);
    }

    /**
     * Deduct money from wallet.
     */
    public function deductMoney($amount, $description, $bookingId = null, $metadata = [], $paymentMethod = 'wallet'): ?WalletTransaction
    {
        if ($this->balance < $amount) {
            return null; // Insufficient balance
        }

        $this->decrement('balance', $amount);

        return WalletTransaction::create([
            'user_id' => $this->user_id,
            'booking_id' => $bookingId,
            'type' => 'debit',
            'amount' => $amount,
            'final_amount' => $amount,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'metadata' => $metadata,
            'payment_method' => $paymentMethod,
        ]);
    }

    /**
     * Add refund to wallet (does not count as earnings).
     */
    public function addRefund($amount, $description, $bookingId = null, $metadata = [], $paymentMethod = 'wallet'): WalletTransaction
    {
        // Only increment balance, not total_earned for refunds
        $this->increment('balance', $amount);

        // Ensure metadata indicates this is a refund
        $metadata['is_refund'] = true;

        return WalletTransaction::create([
            'user_id' => $this->user_id,
            'booking_id' => $bookingId,
            'type' => 'refund',
            'amount' => $amount,
            'final_amount' => $amount,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'metadata' => $metadata,
            'payment_method' => $paymentMethod,
        ]);
    }

    /**
     * Add earnings with commission deduction.
     */
    public function addEarnings($grossAmount, $commissionPercentage, $description, $bookingId = null, $paymentMethod = 'wallet'): WalletTransaction
    {
        $commissionAmount = ($grossAmount * $commissionPercentage) / 100;
        $netAmount = $grossAmount - $commissionAmount;

        $this->increment('balance', $netAmount);
        $this->increment('total_earned', $netAmount);

        return WalletTransaction::create([
            'user_id' => $this->user_id,
            'booking_id' => $bookingId,
            'type' => 'credit',
            'amount' => $grossAmount,
            'commission_amount' => $commissionAmount,
            'final_amount' => $netAmount,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'metadata' => [
                'commission_percentage' => $commissionPercentage,
                'gross_amount' => $grossAmount,
                'commission_amount' => $commissionAmount,
                'net_amount' => $netAmount,
            ],
            'payment_method' => $paymentMethod,
        ]);
    }

    /**
     * Get or create wallet for user.
     */
    public static function getOrCreate($userId): self
    {
        return self::firstOrCreate(['user_id' => $userId]);
    }
}
